<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Settings Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Payment Settings Debug</h1>
    <p>Testing the "Missing or insufficient permissions" error in Payment Settings</p>
    
    <button onclick="testAuthentication()">Test Authentication</button>
    <button onclick="testStripeConnectAccountStatus()">Test getStripeConnectAccountStatus</button>
    <button onclick="testCreateStripeAccount()">Test createStripeConnectAccount</button>
    <button onclick="clearResults()">Clear Results</button>

    <div id="results"></div>

    <script type="module">
        // Import Firebase
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithEmailAndPassword, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyBnF-pVKlXNdwBQ-74SEfw7NHJLLmL5e6A",
            authDomain: "h1c1-798a8.firebaseapp.com",
            projectId: "h1c1-798a8",
            storageBucket: "h1c1-798a8.firebasestorage.app",
            messagingSenderId: "*************",
            appId: "1:*************:web:4caac283b87d55a4ba9c35"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const functions = getFunctions(app);

        // Initialize Firebase functions
        const getStripeConnectAccountStatus = httpsCallable(functions, 'getStripeConnectAccountStatus');
        const createStripeConnectAccount = httpsCallable(functions, 'createStripeConnectAccount');

        let currentUser = null;

        // Monitor auth state
        onAuthStateChanged(auth, (user) => {
            currentUser = user;
            if (user) {
                addResult(`✅ User authenticated: ${user.uid}`, 'success');
                addResult(`Email: ${user.email}`, 'info');
            } else {
                addResult('❌ User not authenticated', 'error');
            }
        });

        // Make functions available globally
        window.testAuthentication = async function() {
            addResult('🧪 Testing authentication...', 'info');
            
            if (currentUser) {
                addResult(`✅ Already authenticated as: ${currentUser.uid}`, 'success');
                addResult(`Email: ${currentUser.email}`, 'info');
                
                // Test if we can get the ID token
                try {
                    const idToken = await currentUser.getIdToken();
                    addResult('✅ ID Token obtained successfully', 'success');
                } catch (error) {
                    addResult(`❌ Failed to get ID token: ${error.message}`, 'error');
                }
            } else {
                addResult('❌ Not authenticated. Please sign in first.', 'error');
                
                // Prompt for email/password
                const email = prompt('Enter your email:');
                const password = prompt('Enter your password:');
                
                if (email && password) {
                    try {
                        const userCredential = await signInWithEmailAndPassword(auth, email, password);
                        addResult(`✅ Signed in successfully: ${userCredential.user.uid}`, 'success');
                        currentUser = userCredential.user;
                    } catch (error) {
                        addResult(`❌ Sign in failed: ${error.message}`, 'error');
                    }
                }
            }
        };

        window.testStripeConnectAccountStatus = async function() {
            if (!currentUser) {
                addResult('❌ Please authenticate first', 'error');
                return;
            }

            addResult('🧪 Testing getStripeConnectAccountStatus...', 'info');
            
            try {
                const result = await getStripeConnectAccountStatus({});
                addResult('✅ Function call successful', 'success');
                addResult(`Response: <pre>${JSON.stringify(result.data, null, 2)}</pre>`, 'info');
                
            } catch (error) {
                addResult(`❌ Function call failed: ${error.message}`, 'error');
                addResult(`Error code: ${error.code}`, 'error');
                addResult(`Error details: <pre>${JSON.stringify(error, null, 2)}</pre>`, 'error');
                
                if (error.code === 'functions/unauthenticated') {
                    addResult('🚨 This is an authentication error - user not properly authenticated', 'error');
                } else if (error.code === 'functions/permission-denied') {
                    addResult('🚨 This is a permission error - user lacks required permissions', 'error');
                } else if (error.code === 'functions/internal') {
                    addResult('🚨 This is an internal server error', 'error');
                } else if (error.message.includes('Missing or insufficient permissions')) {
                    addResult('🚨 This is the exact error from Payment Settings!', 'error');
                }
            }
        };

        window.testCreateStripeAccount = async function() {
            if (!currentUser) {
                addResult('❌ Please authenticate first', 'error');
                return;
            }

            addResult('🧪 Testing createStripeConnectAccount...', 'info');
            
            try {
                const result = await createStripeConnectAccount({ accountType: 'student' });
                addResult('✅ Function call successful', 'success');
                addResult(`Response: <pre>${JSON.stringify(result.data, null, 2)}</pre>`, 'info');
                
            } catch (error) {
                addResult(`❌ Function call failed: ${error.message}`, 'error');
                addResult(`Error code: ${error.code}`, 'error');
                addResult(`Error details: <pre>${JSON.stringify(error, null, 2)}</pre>`, 'error');
            }
        };

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        // Auto-run authentication check
        window.addEventListener('load', () => {
            addResult(`Testing from origin: ${window.location.origin}`, 'info');
            setTimeout(() => {
                if (currentUser) {
                    addResult(`Current user: ${currentUser.uid}`, 'info');
                } else {
                    addResult('No user authenticated yet', 'info');
                }
            }, 1000);
        });
    </script>
</body>
</html>
