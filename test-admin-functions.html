<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Functions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Admin Functions</h1>
        <p>This page tests the Firebase admin functions to ensure they're working correctly.</p>
        
        <div class="test-section">
            <h3>1. Test Essential Webhook</h3>
            <p>Tests if the basic Firebase functions are working.</p>
            <button onclick="testEssentialWebhook()">Test Essential Webhook</button>
            <div id="essential-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Admin PIN Functions (Requires Login)</h3>
            <p>Tests admin PIN verification and wallet settings functions.</p>
            <div>
                <input type="email" id="email" placeholder="Admin Email" value="<EMAIL>">
                <input type="password" id="password" placeholder="Password">
                <button onclick="loginAndTest()">Login & Test Admin Functions</button>
            </div>
            <div id="admin-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. Test PIN Setup Check</h3>
            <p>Tests if PIN setup detection is working correctly.</p>
            <button onclick="testPinSetupCheck()">Test PIN Setup Check</button>
            <div id="pin-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithEmailAndPassword } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBnF-pVKlXNdwBQ-74SEfw7NHJLLmL5e6A",
            authDomain: "h1c1-798a8.firebaseapp.com",
            projectId: "h1c1-798a8",
            storageBucket: "h1c1-798a8.firebasestorage.app",
            messagingSenderId: "1096652648176",
            appId: "1:1096652648176:web:4caac283b87d55a4ba9c35"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const functions = getFunctions(app);

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // Test essential webhook
        window.testEssentialWebhook = async function() {
            try {
                showResult('essential-result', 'Testing essential webhook...', 'info');
                
                const response = await fetch('https://us-central1-h1c1-798a8.cloudfunctions.net/testEssential');
                const data = await response.json();
                
                showResult('essential-result', `✅ Success!\n${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResult('essential-result', `❌ Error: ${error.message}`, 'error');
            }
        };

        // Login and test admin functions
        window.loginAndTest = async function() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showResult('admin-result', '❌ Please enter email and password', 'error');
                return;
            }

            try {
                showResult('admin-result', 'Logging in...', 'info');
                
                // Login
                await signInWithEmailAndPassword(auth, email, password);
                showResult('admin-result', '✅ Logged in successfully!\nTesting admin functions...', 'info');
                
                // Test verifyAdminPin with dummy PIN
                try {
                    const verifyPin = httpsCallable(functions, 'verifyAdminPin');
                    await verifyPin({ pin: '00000000' });
                } catch (pinError) {
                    console.log('PIN test error (expected):', pinError);
                    
                    if (pinError.code === 'functions/not-found') {
                        showResult('admin-result', '✅ PIN functions working!\n❗ PIN not set up yet (this is expected)', 'success');
                    } else if (pinError.code === 'functions/permission-denied') {
                        showResult('admin-result', '✅ PIN functions working!\n✅ PIN is already set up', 'success');
                    } else {
                        showResult('admin-result', `❌ PIN function error: ${pinError.message}`, 'error');
                        return;
                    }
                }
                
                // Test getWalletSettings
                try {
                    const getWalletSettings = httpsCallable(functions, 'getWalletSettings');
                    const result = await getWalletSettings({});
                    showResult('admin-result', `✅ All admin functions working!\n\nWallet Settings:\n${JSON.stringify(result.data, null, 2)}`, 'success');
                } catch (walletError) {
                    showResult('admin-result', `❌ Wallet settings error: ${walletError.message}`, 'error');
                }
                
            } catch (error) {
                showResult('admin-result', `❌ Login error: ${error.message}`, 'error');
            }
        };

        // Test PIN setup check
        window.testPinSetupCheck = async function() {
            try {
                showResult('pin-result', 'Testing PIN setup check...', 'info');
                
                const verifyPin = httpsCallable(functions, 'verifyAdminPin');
                await verifyPin({ pin: '00000000' });
                
                showResult('pin-result', '❌ Unexpected: PIN verification succeeded with dummy PIN', 'error');
            } catch (error) {
                console.log('PIN setup check error:', error);
                
                if (error.code === 'functions/not-found') {
                    showResult('pin-result', '✅ PIN setup check working!\n❗ PIN not set up (setup mode should be enabled)', 'success');
                } else if (error.code === 'functions/permission-denied') {
                    showResult('pin-result', '✅ PIN setup check working!\n✅ PIN is set up (normal mode)', 'success');
                } else if (error.code === 'functions/unauthenticated') {
                    showResult('pin-result', '✅ PIN setup check working!\n❗ Not authenticated (login required)', 'info');
                } else {
                    showResult('pin-result', `❌ Unexpected error: ${error.message}`, 'error');
                }
            }
        };
    </script>
</body>
</html>
