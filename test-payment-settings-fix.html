<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Settings Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Payment Settings Fix Test</h1>
    <p>Testing if the "Missing or insufficient permissions" error is fixed</p>
    
    <button onclick="testFirestoreAccess()">Test Firestore connectAccounts Access</button>
    <button onclick="testStripeConnectFunctions()">Test Stripe Connect Functions</button>
    <button onclick="clearResults()">Clear Results</button>

    <div id="results"></div>

    <script type="module">
        // Import Firebase
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithEmailAndPassword, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, doc, getDoc, setDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyBnF-pVKlXNdwBQ-74SEfw7NHJLLmL5e6A",
            authDomain: "h1c1-798a8.firebaseapp.com",
            projectId: "h1c1-798a8",
            storageBucket: "h1c1-798a8.firebasestorage.app",
            messagingSenderId: "*************",
            appId: "1:*************:web:4caac283b87d55a4ba9c35"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const firestore = getFirestore(app);
        const functions = getFunctions(app);

        // Initialize Firebase functions
        const getStripeConnectAccountStatus = httpsCallable(functions, 'getStripeConnectAccountStatus');

        let currentUser = null;

        // Monitor auth state
        onAuthStateChanged(auth, (user) => {
            currentUser = user;
            if (user) {
                addResult(`✅ User authenticated: ${user.uid}`, 'success');
            } else {
                addResult('❌ User not authenticated', 'error');
            }
        });

        // Make functions available globally
        window.testFirestoreAccess = async function() {
            if (!currentUser) {
                addResult('❌ Please sign in first', 'error');
                
                // Prompt for email/password
                const email = prompt('Enter your email:');
                const password = prompt('Enter your password:');
                
                if (email && password) {
                    try {
                        const userCredential = await signInWithEmailAndPassword(auth, email, password);
                        addResult(`✅ Signed in successfully: ${userCredential.user.uid}`, 'success');
                        currentUser = userCredential.user;
                    } catch (error) {
                        addResult(`❌ Sign in failed: ${error.message}`, 'error');
                        return;
                    }
                } else {
                    return;
                }
            }

            addResult('🧪 Testing Firestore connectAccounts access...', 'info');
            
            try {
                // Test reading from connectAccounts collection
                const connectAccountDoc = doc(firestore, 'connectAccounts', currentUser.uid);
                const docSnap = await getDoc(connectAccountDoc);
                
                if (docSnap.exists()) {
                    addResult('✅ Successfully read existing connectAccount document', 'success');
                    addResult(`Data: <pre>${JSON.stringify(docSnap.data(), null, 2)}</pre>`, 'info');
                } else {
                    addResult('ℹ️ No connectAccount document found (this is normal for new users)', 'info');
                    
                    // Test writing to connectAccounts collection
                    addResult('🧪 Testing write access to connectAccounts...', 'info');
                    
                    const testData = {
                        userId: currentUser.uid,
                        stripeAccountId: 'test_account_id',
                        accountType: 'student',
                        isOnboarded: false,
                        chargesEnabled: false,
                        payoutsEnabled: false,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                    };
                    
                    await setDoc(connectAccountDoc, testData);
                    addResult('✅ Successfully wrote test connectAccount document', 'success');
                    
                    // Read it back to confirm
                    const confirmDoc = await getDoc(connectAccountDoc);
                    if (confirmDoc.exists()) {
                        addResult('✅ Successfully read back the test document', 'success');
                    }
                }
                
            } catch (error) {
                addResult(`❌ Firestore access failed: ${error.message}`, 'error');
                
                if (error.message.includes('Missing or insufficient permissions')) {
                    addResult('🚨 This is the exact error from Payment Settings!', 'error');
                    addResult('💡 The Firestore rules may not be deployed yet', 'info');
                } else if (error.code === 'permission-denied') {
                    addResult('🚨 Permission denied - Firestore rules issue', 'error');
                }
            }
        };

        window.testStripeConnectFunctions = async function() {
            if (!currentUser) {
                addResult('❌ Please authenticate first', 'error');
                return;
            }

            addResult('🧪 Testing Stripe Connect functions...', 'info');
            
            try {
                const result = await getStripeConnectAccountStatus({});
                addResult('✅ getStripeConnectAccountStatus function call successful', 'success');
                addResult(`Response: <pre>${JSON.stringify(result.data, null, 2)}</pre>`, 'info');
                
            } catch (error) {
                addResult(`❌ Function call failed: ${error.message}`, 'error');
                addResult(`Error code: ${error.code}`, 'error');
            }
        };

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        // Auto-run info
        window.addEventListener('load', () => {
            addResult(`Testing from origin: ${window.location.origin}`, 'info');
            addResult('This test verifies that the Payment Settings "Missing or insufficient permissions" error is fixed', 'info');
            addResult('1. Firestore rules now allow access to connectAccounts collection', 'info');
            addResult('2. PaymentSettings component now uses useStripeConnect hook instead of direct Firestore access', 'info');
        });
    </script>
</body>
</html>
