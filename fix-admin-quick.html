<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Admin User</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix Admin User</h1>
        <p>This tool will fix the admin user permissions for Hive Campus.</p>
        
        <form id="fixAdminForm">
            <div class="form-group">
                <label for="email">Admin Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <button type="submit" id="fixButton">Fix Admin User</button>
        </form>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBnF-pVKlXNdwBQ-74SEfw7NHJLLmL5e6A",
            authDomain: "h1c1-798a8.firebaseapp.com",
            projectId: "h1c1-798a8",
            storageBucket: "h1c1-798a8.firebasestorage.app",
            messagingSenderId: "1096652648176",
            appId: "1:1096652648176:web:4caac283b87d55a4ba9c35"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const functions = getFunctions(app);

        // Get DOM elements
        const form = document.getElementById('fixAdminForm');
        const emailInput = document.getElementById('email');
        const fixButton = document.getElementById('fixButton');
        const status = document.getElementById('status');

        function showStatus(message, type) {
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = emailInput.value.trim();
            if (!email) {
                showStatus('Please enter an email address', 'error');
                return;
            }

            fixButton.disabled = true;
            fixButton.textContent = 'Fixing...';
            showStatus('Fixing admin user...', 'info');

            try {
                const fixAdminUser = httpsCallable(functions, 'fixAdminUser');
                const result = await fixAdminUser({ email });
                
                showStatus(`✅ Success! ${result.data.message}`, 'success');
                console.log('Admin user fixed:', result.data);
                
                // Show next steps
                setTimeout(() => {
                    showStatus('✅ Admin user fixed! You can now login and access the admin dashboard.', 'success');
                }, 2000);
                
            } catch (error) {
                console.error('Error fixing admin user:', error);
                showStatus(`❌ Error: ${error.message}`, 'error');
            } finally {
                fixButton.disabled = false;
                fixButton.textContent = 'Fix Admin User';
            }
        });
    </script>
</body>
</html>
