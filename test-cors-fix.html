<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 CORS Fix Test for Hive Campus Functions</h1>
    <p>This page tests if the CORS issues have been resolved for the Firebase Cloud Functions.</p>
    
    <div class="test-result info">
        <strong>Testing from origin:</strong> <span id="origin"></span>
    </div>

    <button onclick="testGetStripeConnectAccountStatus()">Test getStripeConnectAccountStatus</button>
    <button onclick="testGetSellerPendingPayouts()">Test getSellerPendingPayouts</button>
    <button onclick="testCreateStripeConnectAccount()">Test createStripeConnectAccount</button>
    <button onclick="clearResults()">Clear Results</button>

    <div id="results"></div>

    <script type="module">
        // Set the origin
        document.getElementById('origin').textContent = window.location.origin;

        // Import Firebase (you'll need to replace with your actual config)
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInAnonymously } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyBnF-pVKlXNdwBQ-74SEfw7NHJLLmL5e6A",
            authDomain: "h1c1-798a8.firebaseapp.com",
            projectId: "h1c1-798a8",
            storageBucket: "h1c1-798a8.firebasestorage.app",
            messagingSenderId: "*************",
            appId: "1:*************:web:4caac283b87d55a4ba9c35"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const functions = getFunctions(app);

        // Sign in anonymously for testing
        try {
            await signInAnonymously(auth);
            addResult('✅ Successfully signed in anonymously', 'success');
        } catch (error) {
            addResult('❌ Failed to sign in: ' + error.message, 'error');
        }

        // Make functions available globally
        window.testGetStripeConnectAccountStatus = async function() {
            await testFunction('getStripeConnectAccountStatus', {});
        };

        window.testGetSellerPendingPayouts = async function() {
            await testFunction('getSellerPendingPayouts', {});
        };

        window.testCreateStripeConnectAccount = async function() {
            await testFunction('createStripeConnectAccount', { accountType: 'student' });
        };

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        async function testFunction(functionName, data) {
            const startTime = Date.now();
            addResult(`🧪 Testing ${functionName}...`, 'info');
            
            try {
                const callable = httpsCallable(functions, functionName);
                const result = await callable(data);
                const duration = Date.now() - startTime;
                
                addResult(`✅ ${functionName} succeeded in ${duration}ms`, 'success');
                addResult(`Response: <pre>${JSON.stringify(result.data, null, 2)}</pre>`, 'info');
                
            } catch (error) {
                const duration = Date.now() - startTime;
                addResult(`❌ ${functionName} failed in ${duration}ms`, 'error');
                addResult(`Error: ${error.message}`, 'error');
                
                // Check if it's a CORS error
                if (error.message.includes('CORS') || error.message.includes('Access-Control-Allow-Origin')) {
                    addResult('🚨 This appears to be a CORS error - the fix may not be working', 'error');
                } else if (error.code === 'functions/unauthenticated') {
                    addResult('🔐 Authentication error - this is expected for some functions', 'info');
                } else {
                    addResult(`Error details: <pre>${JSON.stringify(error, null, 2)}</pre>`, 'error');
                }
            }
        }

        function addResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
    </script>
</body>
</html>
