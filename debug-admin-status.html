<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Admin Status</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .status-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 250px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Admin Status</h1>
        <p>This tool helps debug admin authentication issues.</p>
        
        <div class="status-section">
            <h3>1. Session Storage Status</h3>
            <button onclick="checkSessionStorage()">Check Session Storage</button>
            <div id="session-result" class="result" style="display: none;"></div>
        </div>

        <div class="status-section">
            <h3>2. Firebase Auth Status</h3>
            <div>
                <input type="email" id="email" placeholder="Admin Email" value="<EMAIL>">
                <input type="password" id="password" placeholder="Password">
                <button onclick="checkFirebaseAuth()">Login & Check Auth Status</button>
            </div>
            <div id="auth-result" class="result" style="display: none;"></div>
        </div>

        <div class="status-section">
            <h3>3. Admin Functions Test</h3>
            <button onclick="testAdminFunctions()">Test Admin Functions</button>
            <div id="functions-result" class="result" style="display: none;"></div>
        </div>

        <div class="status-section">
            <h3>4. Quick Fixes</h3>
            <button onclick="clearAllSessions()">Clear All Sessions</button>
            <button onclick="forceLogout()">Force Logout</button>
            <div id="fixes-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithEmailAndPassword, signOut } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBnF-pVKlXNdwBQ-74SEfw7NHJLLmL5e6A",
            authDomain: "h1c1-798a8.firebaseapp.com",
            projectId: "h1c1-798a8",
            storageBucket: "h1c1-798a8.firebasestorage.app",
            messagingSenderId: "1096652648176",
            appId: "1:1096652648176:web:4caac283b87d55a4ba9c35"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const functions = getFunctions(app);

        function showResult(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.style.display = 'block';
        }

        window.checkSessionStorage = function() {
            const pinVerified = sessionStorage.getItem('adminPinVerified');
            const pinTime = sessionStorage.getItem('adminPinTime');
            
            let result = 'Session Storage Status:\n';
            result += `adminPinVerified: ${pinVerified}\n`;
            result += `adminPinTime: ${pinTime}\n`;
            
            if (pinVerified === 'true' && pinTime) {
                const timeDiff = Date.now() - parseInt(pinTime);
                const minutesAgo = Math.floor(timeDiff / (1000 * 60));
                result += `PIN verified ${minutesAgo} minutes ago\n`;
                result += `Time remaining: ${240 - minutesAgo} minutes\n`;
            }
            
            showResult('session-result', result);
        };

        window.checkFirebaseAuth = async function() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showResult('auth-result', 'Please enter email and password');
                return;
            }

            try {
                showResult('auth-result', 'Logging in...');
                
                const userCredential = await signInWithEmailAndPassword(auth, email, password);
                const user = userCredential.user;
                
                // Get ID token with claims
                const idTokenResult = await user.getIdTokenResult(true);
                
                let result = 'Firebase Auth Status:\n';
                result += `UID: ${user.uid}\n`;
                result += `Email: ${user.email}\n`;
                result += `Email Verified: ${user.emailVerified}\n`;
                result += `Display Name: ${user.displayName}\n`;
                result += '\nCustom Claims:\n';
                result += JSON.stringify(idTokenResult.claims, null, 2);
                
                showResult('auth-result', result);
                
            } catch (error) {
                showResult('auth-result', `Error: ${error.message}`);
            }
        };

        window.testAdminFunctions = async function() {
            try {
                showResult('functions-result', 'Testing admin functions...');
                
                let result = 'Admin Functions Test:\n\n';
                
                // Test verifyAdminPin
                try {
                    const verifyPin = httpsCallable(functions, 'verifyAdminPin');
                    await verifyPin({ pin: '00000000' });
                    result += '❌ verifyAdminPin: Unexpected success\n';
                } catch (error) {
                    result += `✅ verifyAdminPin: ${error.code} - ${error.message}\n`;
                }
                
                // Test getWalletSettings
                try {
                    const getWalletSettings = httpsCallable(functions, 'getWalletSettings');
                    const walletResult = await getWalletSettings({});
                    result += `✅ getWalletSettings: Success\n${JSON.stringify(walletResult.data, null, 2)}\n`;
                } catch (error) {
                    result += `❌ getWalletSettings: ${error.code} - ${error.message}\n`;
                }
                
                showResult('functions-result', result);
                
            } catch (error) {
                showResult('functions-result', `Error: ${error.message}`);
            }
        };

        window.clearAllSessions = function() {
            sessionStorage.clear();
            localStorage.clear();
            showResult('fixes-result', '✅ All sessions cleared');
        };

        window.forceLogout = async function() {
            try {
                await signOut(auth);
                sessionStorage.clear();
                localStorage.clear();
                showResult('fixes-result', '✅ Logged out and sessions cleared');
            } catch (error) {
                showResult('fixes-result', `Error: ${error.message}`);
            }
        };
    </script>
</body>
</html>
