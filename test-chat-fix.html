<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Chat Functionality Test</h1>
    <p>Testing Firestore chat operations to verify the 400 Bad Request error is fixed</p>
    
    <button onclick="testFirestoreConnection()">Test Firestore Connection</button>
    <button onclick="testChatCreation()">Test Chat Creation</button>
    <button onclick="clearResults()">Clear Results</button>

    <div id="results"></div>

    <script type="module">
        // Import Firebase
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInAnonymously } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, doc, setDoc, collection, addDoc, serverTimestamp, Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyBnF-pVKlXNdwBQ-74SEfw7NHJLLmL5e6A",
            authDomain: "h1c1-798a8.firebaseapp.com",
            projectId: "h1c1-798a8",
            storageBucket: "h1c1-798a8.firebasestorage.app",
            messagingSenderId: "1096652648176",
            appId: "1:1096652648176:web:4caac283b87d55a4ba9c35"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const firestore = getFirestore(app);

        // Sign in anonymously for testing
        let currentUser = null;
        try {
            const userCredential = await signInAnonymously(auth);
            currentUser = userCredential.user;
            addResult('✅ Successfully signed in anonymously: ' + currentUser.uid, 'success');
        } catch (error) {
            addResult('❌ Failed to sign in: ' + error.message, 'error');
        }

        // Make functions available globally
        window.testFirestoreConnection = async function() {
            addResult('🧪 Testing Firestore connection...', 'info');
            
            try {
                // Test basic Firestore connectivity
                const testDoc = doc(firestore, 'test', 'connection-test');
                await setDoc(testDoc, {
                    timestamp: Timestamp.now(),
                    message: 'Connection test',
                    userId: currentUser?.uid || 'anonymous'
                });
                
                addResult('✅ Firestore connection successful', 'success');
                
            } catch (error) {
                addResult('❌ Firestore connection failed: ' + error.message, 'error');
                
                if (error.message.includes('firestore.googleapis.com')) {
                    addResult('🚨 This appears to be the same Firestore API error', 'error');
                }
            }
        };

        window.testChatCreation = async function() {
            if (!currentUser) {
                addResult('❌ Please sign in first', 'error');
                return;
            }

            addResult('🧪 Testing chat creation...', 'info');
            
            try {
                // Create a test chat
                const chatId = `test_chat_${Date.now()}`;
                const chatRef = doc(firestore, 'chats', chatId);
                
                const chatData = {
                    participants: [currentUser.uid, 'test_user_2'],
                    createdAt: Timestamp.now(),
                    updatedAt: Timestamp.now(),
                    lastMessage: null
                };

                await setDoc(chatRef, chatData);
                addResult('✅ Chat creation successful', 'success');

                // Test message creation
                addResult('🧪 Testing message creation...', 'info');
                
                const messagesRef = collection(firestore, `chats/${chatId}/messages`);
                const messageData = {
                    senderId: currentUser.uid,
                    receiverId: 'test_user_2',
                    text: 'Test message',
                    type: 'text',
                    createdAt: Timestamp.now(),
                    read: false
                };

                const messageDoc = await addDoc(messagesRef, messageData);
                addResult('✅ Message creation successful: ' + messageDoc.id, 'success');
                
            } catch (error) {
                addResult('❌ Chat/Message creation failed: ' + error.message, 'error');
                
                if (error.message.includes('firestore.googleapis.com') || 
                    error.message.includes('Write/channel') ||
                    error.message.includes('400')) {
                    addResult('🚨 This appears to be the Firestore Write/channel error', 'error');
                    addResult('💡 Try refreshing the page and testing again', 'info');
                }
            }
        };

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        // Auto-run connection test
        window.addEventListener('load', () => {
            addResult(`Testing from origin: ${window.location.origin}`, 'info');
            addResult(`Current user: ${currentUser?.uid || 'Not signed in'}`, 'info');
        });
    </script>
</body>
</html>
